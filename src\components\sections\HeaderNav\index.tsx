import React from "react";
import Container from "../../Container";

export default function HeaderNav() {
  return (
    <header className="sticky top-0 z-50 backdrop-blur supports-[backdrop-filter]:bg-white/60 bg-white/80 dark:bg-zinc-950/60 border-b">
      <Container className="h-14 flex items-center justify-between">
        <div className="font-heading font-semibold">Brand</div>
        <nav className="hidden sm:flex items-center gap-6 text-sm">
          <a href="#benefits" className="hover:underline">Benefits</a>
          <a href="#location" className="hover:underline">Lokasi</a>
          <a href="#bestsellers" className="hover:underline">Best Sellers</a>
          <a href="#testimonials" className="hover:underline">Testimoni</a>
          <a href="#about" className="hover:underline">Tentang <PERSON></a>
          <a href="#gallery" className="hover:underline"><PERSON>ri</a>
          <a href="#faq" className="hover:underline">FAQ</a>
        </nav>
        <a className="inline-flex h-9 items-center justify-center rounded-md bg-black text-white px-4 text-sm hover:opacity-90">
          Contact
        </a>
      </Container>
    </header>
  );
}

